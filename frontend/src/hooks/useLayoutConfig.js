import { useLocation } from 'react-router-dom';

/**
 * Hook to determine layout configuration for different routes
 * This allows you to easily control which pages should have sidebar, header, footer, etc.
 */
export const useLayoutConfig = () => {
  const location = useLocation();

  // Define routes that should not have the main website layout
  const noSidebarRoutes = [
    '/admin',
    '/admin-dashboard',
    '/admin-debug',
    '/about',
    // Add more routes here as needed
  ];

  // Define routes that should not have header
  const noHeaderRoutes = [
    '/admin',
    '/admin-dashboard',
    '/admin-debug',
    // Add more routes here as needed
  ];

  // Define routes that should not have footer
  const noFooterRoutes = [
    '/admin',
    '/admin-dashboard',
    '/admin-debug',
    // Add more routes here as needed
  ];

  // Helper function to check if current route matches any pattern
  const matchesRoute = (routes) => {
    return routes.some(route => 
      location.pathname.includes(route) || 
      location.pathname.match(new RegExp(`/[a-z]{2}${route}`)) // Match language prefixed routes
    );
  };

  return {
    shouldShowSidebar: !matchesRoute(noSidebarRoutes),
    shouldShowHeader: !matchesRoute(noHeaderRoutes),
    shouldShowFooter: !matchesRoute(noFooterRoutes),
    isAdminRoute: matchesRoute(['/admin']),
    currentPath: location.pathname
  };
};

/**
 * Configuration object for easy route management
 * You can import this and modify it to add new routes
 */
export const LAYOUT_CONFIG = {
  NO_SIDEBAR_ROUTES: [
    '/admin',
    '/admin-dashboard',
    '/admin-debug',
  ],
  NO_HEADER_ROUTES: [
    '/admin',
    '/admin-dashboard', 
    '/admin-debug',
  ],
  NO_FOOTER_ROUTES: [
    '/admin',
    '/admin-dashboard',
    '/admin-debug',
  ]
};

export default useLayoutConfig;
